using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Apis;
using Otg.Keycloak.Tenants.Application.Dto;
using Otg.Keycloak.Tenants.Application.Requests;
using Otg.Keycloak.Tenants.Domain.Aggregates;
using Otg.Keycloak.Tenants.Domain.Entities;
using Otg.Keycloak.Tenants.Infrastructure.KeyCloakApi.Services;

namespace Otg.Keycloak.Tenants.Application.UnitTests.Common.Services;

public class ClientServiceTests{

    private IKeycloakService _keycloakService;
    private ClientService _clientService;
    private TokenDto mockTokenDto;
    private RealmRequest realmRequest;

    public ClientServiceTests(){
        _keycloakService = Substitute.For<IKeycloakService>();

        mockTokenDto =TestHelpers.mockTokenDto;

        realmRequest = TestHelpers.realmRequest;
    }

    [Fact]
    public async Task GetClientsCallsRequestWithTokenWithCorrectParameters(){
        var clientList = new List<ClientDto>{
            new ClientDto{ClientId="id1",Name="OTG",Protocol="protocol1",Enabled=true,PublicClient=true, StandardFlowEnabled=true},
            new ClientDto{ClientId="id2",Name="LloydsRegister",Protocol="protocol2",Enabled=true,PublicClient=false, StandardFlowEnabled=true}
        };
        var keyCloakApiResut = new KeycloakApiResult<List<ClientDto>>{
            Response = clientList,
            ResponseRaw = "testResponse",
            Success = false
        };
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients";

        _keycloakService.RequestWithToken<List<ClientDto>>(Arg.Any<HttpMethod>(),
            Arg.Any<string>(),Arg.Any<TokenDto>(),Arg.Any<string>())
            .Returns(Task.FromResult(keyCloakApiResut));

        _clientService = new ClientService(_keycloakService);
        var result = _clientService.GetClients(realmRequest);

        await _keycloakService.Received(1).RequestWithToken<List<ClientDto>>(HttpMethod.Get,requestUri,Arg.Is<TokenDto>(token =>(
            token.AccessToken == mockTokenDto.AccessToken &&
            token.RefreshToken == mockTokenDto.RefreshToken &&
            token.ExpiresIn == mockTokenDto.ExpiresIn &&
            token.TokenType == mockTokenDto.TokenType
        )));
    }

    [Fact]
    public async Task CreateClientWithClientDtoCallsRequestWithTokenWithCorrectParameters(){
        var clientDto = new ClientDto{
            ClientId="id1",
            Name="OTG",
            Protocol="protocol1",
            Enabled=true,
            PublicClient=true,
            StandardFlowEnabled=true};

        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients";

        var keyCloakApiResut = new KeycloakApiResult<object>{
            Response = "test-Response",
            ResponseRaw = "testResponse",
            Success = false
        };

        _keycloakService.RequestWithToken<object>(Arg.Any<HttpMethod>(),
            Arg.Any<string>(),Arg.Any<TokenDto>(),Arg.Any<string>())
            .Returns(Task.FromResult(keyCloakApiResut));

        _clientService = new ClientService(_keycloakService);
        var result = _clientService.CreateClient(realmRequest,clientDto);

        await _keycloakService.Received(1).RequestWithToken<object>(HttpMethod.Post,requestUri,Arg.Is<TokenDto>(token =>(
            token.AccessToken == mockTokenDto.AccessToken &&
            token.RefreshToken == mockTokenDto.RefreshToken &&
            token.ExpiresIn == mockTokenDto.ExpiresIn &&
            token.TokenType == mockTokenDto.TokenType
        )),JsonConvert.SerializeObject(clientDto));
    }

    [Fact]
    public async Task CreateClientWithJObjectCallsRequestWithTokenWithCorrectParameters(){
        var jObject = new JObject();

        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients";

        var keyCloakApiResut = new KeycloakApiResult<object>{
            Response = "test-Response",
            ResponseRaw = "testResponse",
            Success = false
        };

        _keycloakService.RequestWithToken<object>(Arg.Any<HttpMethod>(),
            Arg.Any<string>(),Arg.Any<TokenDto>(),Arg.Any<string>())
            .Returns(Task.FromResult(keyCloakApiResut));

        _clientService = new ClientService(_keycloakService);
        var result = _clientService.CreateClient(realmRequest,jObject);

        await _keycloakService.Received(1).RequestWithToken<object>(HttpMethod.Post,requestUri,Arg.Is<TokenDto>(token =>(
            token.AccessToken == mockTokenDto.AccessToken &&
            token.RefreshToken == mockTokenDto.RefreshToken &&
            token.ExpiresIn == mockTokenDto.ExpiresIn &&
            token.TokenType == mockTokenDto.TokenType
        )),JsonConvert.SerializeObject(jObject));
    }

    [Fact]
    public async Task OverrideAuthenticationFlowCallsRequestWithTokenWithCorrectParameters(){
        var overrideAuthenticationFlowDto = new OverrideAuthenticationFlowDto{
            AuthenticationFlowBindingOverrides = new AuthenticationFlowBindingOverridesDto{Browser = Guid.NewGuid(),
             DirectGrant = "testGrant"}
        };

        var keyCloakApiResut = new KeycloakApiResult<object>{
            Response = "test-Response",
            ResponseRaw = "testResponse",
            Success = false
        };

        var clientIdGuid = Guid.NewGuid();
        var clientId = clientIdGuid.ToString();
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}";
        _keycloakService.RequestWithToken<object>(Arg.Any<HttpMethod>(),
            Arg.Any<string>(),Arg.Any<TokenDto>(),Arg.Any<string>())
            .Returns(Task.FromResult(keyCloakApiResut));

        _clientService = new ClientService(_keycloakService);
        var result = _clientService.OverrideAuthenticationFlow(realmRequest,clientIdGuid,overrideAuthenticationFlowDto);

        await _keycloakService.Received(1).RequestWithToken<object>(HttpMethod.Put,requestUri,Arg.Is<TokenDto>(token =>(
            token.AccessToken == mockTokenDto.AccessToken &&
            token.RefreshToken == mockTokenDto.RefreshToken &&
            token.ExpiresIn == mockTokenDto.ExpiresIn &&
            token.TokenType == mockTokenDto.TokenType
        )),JsonConvert.SerializeObject(overrideAuthenticationFlowDto));
    }

    [Fact]
    public async Task GetClientReturnsClientWithMatchingClientName(){
        var clientList = new List<ClientDto>{
            new ClientDto{ClientId="id1",Name="OTG",Protocol="protocol1",Enabled=true,PublicClient=true, StandardFlowEnabled=true},
            new ClientDto{ClientId="id2",Name="LloydsRegister",Protocol="protocol2",Enabled=true,PublicClient=false, StandardFlowEnabled=true}
        };

        var keyCloakApiResut = new KeycloakApiResult<List<ClientDto>>{
            Response = clientList,
            ResponseRaw = "testResponse",
            Success = false
        };
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients";

        _keycloakService.RequestWithToken<List<ClientDto>>(Arg.Any<HttpMethod>(),
            Arg.Any<string>(),Arg.Any<TokenDto>(),Arg.Any<string>())
            .Returns(Task.FromResult(keyCloakApiResut));

        _clientService = new ClientService(_keycloakService);
        var result = await _clientService.GetClient(realmRequest,"OTG");
        result.Name.Should().Be("OTG");
        result.ClientId.Should().Be("id1");
    }

    [Fact]
    public async Task GetServiceAccountUserId_ReturnsUserId_WhenSuccessful()
    {
        // Arrange
        var clientId = Guid.NewGuid();
        var expectedUserId = "test-user-id-123";
        var responseJson = new JObject { ["id"] = expectedUserId };

        var keyCloakApiResult = new KeycloakApiResult<JObject>
        {
            Response = responseJson,
            ResponseRaw = responseJson.ToString(),
            Success = true
        };

        var expectedRequestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}/service-account-user";

        _keycloakService.RequestWithToken<JObject>(HttpMethod.Get, expectedRequestUri, realmRequest.Token)
            .Returns(Task.FromResult(keyCloakApiResult));

        _clientService = new ClientService(_keycloakService);

        // Act
        var result = await _clientService.GetServiceAccountUserId(realmRequest, clientId);

        // Assert
        result.Should().Be(expectedUserId);
        await _keycloakService.Received(1).RequestWithToken<JObject>(HttpMethod.Get, expectedRequestUri, realmRequest.Token);
    }

    [Fact]
    public async Task GetServiceAccountUserId_ReturnsNull_WhenRequestFails()
    {
        // Arrange
        var clientId = Guid.NewGuid();

        var keyCloakApiResult = new KeycloakApiResult<JObject>
        {
            Response = new JObject(),
            ResponseRaw = "error response",
            Success = false
        };

        _keycloakService.RequestWithToken<JObject>(Arg.Any<HttpMethod>(), Arg.Any<string>(), Arg.Any<TokenDto>())
            .Returns(Task.FromResult(keyCloakApiResult));

        _clientService = new ClientService(_keycloakService);

        // Act
        var result = await _clientService.GetServiceAccountUserId(realmRequest, clientId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetServiceAccountUserId_ReturnsNull_WhenResponseHasNoId()
    {
        // Arrange
        var clientId = Guid.NewGuid();
        var responseJson = new JObject { ["name"] = "some-name" }; // No "id" field

        var keyCloakApiResult = new KeycloakApiResult<JObject>
        {
            Response = responseJson,
            ResponseRaw = responseJson.ToString(),
            Success = true
        };

        _keycloakService.RequestWithToken<JObject>(Arg.Any<HttpMethod>(), Arg.Any<string>(), Arg.Any<TokenDto>())
            .Returns(Task.FromResult(keyCloakApiResult));

        _clientService = new ClientService(_keycloakService);

        // Act
        var result = await _clientService.GetServiceAccountUserId(realmRequest, clientId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetClientRoleRepresentation_ReturnsRole_WhenSuccessful()
    {
        // Arrange
        var clientId = Guid.NewGuid();
        var roleName = "test-role";
        var roleResponse = new JObject
        {
            ["id"] = "role-id-123",
            ["name"] = roleName,
            ["description"] = "Test role description"
        };

        var keyCloakApiResult = new KeycloakApiResult<JObject>
        {
            Response = roleResponse,
            ResponseRaw = roleResponse.ToString(),
            Success = true
        };

        var expectedRequestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}/roles/{roleName}";

        _keycloakService.RequestWithToken<JObject>(HttpMethod.Get, expectedRequestUri, realmRequest.Token)
            .Returns(Task.FromResult(keyCloakApiResult));

        _clientService = new ClientService(_keycloakService);

        // Act
        var result = await _clientService.GetClientRoleRepresentation(realmRequest, clientId, roleName);

        // Assert
        result.Should().NotBeNull();
        result!["name"]!.ToString().Should().Be(roleName);
        result["id"]!.ToString().Should().Be("role-id-123");
        await _keycloakService.Received(1).RequestWithToken<JObject>(HttpMethod.Get, expectedRequestUri, realmRequest.Token);
    }

    [Fact]
    public async Task GetClientRoleRepresentation_ReturnsNull_WhenRequestFails()
    {
        // Arrange
        var clientId = Guid.NewGuid();
        var roleName = "test-role";

        var keyCloakApiResult = new KeycloakApiResult<JObject>
        {
            Response = null,
            ResponseRaw = "error response",
            Success = false
        };

        _keycloakService.RequestWithToken<JObject>(Arg.Any<HttpMethod>(), Arg.Any<string>(), Arg.Any<TokenDto>())
            .Returns(Task.FromResult(keyCloakApiResult));

        _clientService = new ClientService(_keycloakService);

        // Act
        var result = await _clientService.GetClientRoleRepresentation(realmRequest, clientId, roleName);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task AssignClientRoleToUser_CallsKeycloakServiceWithCorrectParameters()
    {
        // Arrange
        var clientId = Guid.NewGuid();
        var userId = "test-user-id";
        var role = new JObject
        {
            ["id"] = "role-id-123",
            ["name"] = "test-role"
        };

        var keyCloakApiResult = new KeycloakApiResult<object>
        {
            Response = new { message = "Success" },
            ResponseRaw = "success response",
            Success = true
        };

        var expectedRequestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/users/{userId}/role-mappings/clients/{clientId}";
        var expectedRolesArray = new JArray { role };

        _keycloakService.RequestWithToken<object>(HttpMethod.Post, expectedRequestUri, realmRequest.Token, expectedRolesArray.ToString())
            .Returns(Task.FromResult(keyCloakApiResult));

        _clientService = new ClientService(_keycloakService);

        // Act
        await _clientService.AssignClientRoleToUser(realmRequest, clientId, userId, role);

        // Assert
        await _keycloakService.Received(1).RequestWithToken<object>(
            HttpMethod.Post,
            expectedRequestUri,
            realmRequest.Token,
            expectedRolesArray.ToString()
        );
    }

    [Fact]
    public async Task AssignClientRoleToUser_HandlesMultipleRolesInArray()
    {
        // Arrange
        var clientId = Guid.NewGuid();
        var userId = "test-user-id";
        var role = new JObject
        {
            ["id"] = "role-id-123",
            ["name"] = "test-role",
            ["description"] = "Test role"
        };

        var keyCloakApiResult = new KeycloakApiResult<object>
        {
            Response = new { message = "Success" },
            ResponseRaw = "success response",
            Success = true
        };

        _keycloakService.RequestWithToken<object>(Arg.Any<HttpMethod>(), Arg.Any<string>(), Arg.Any<TokenDto>(), Arg.Any<string>())
            .Returns(Task.FromResult(keyCloakApiResult));

        _clientService = new ClientService(_keycloakService);

        // Act
        await _clientService.AssignClientRoleToUser(realmRequest, clientId, userId, role);

        // Assert - Verify that the role was wrapped in a JArray
        await _keycloakService.Received(1).RequestWithToken<object>(
            Arg.Any<HttpMethod>(),
            Arg.Any<string>(),
            Arg.Any<TokenDto>(),
            Arg.Is<string>(body => body.Contains(role["name"]!.ToString()) && body.StartsWith("[") && body.EndsWith("]"))
        );
    }

}