
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using Otg.Keycloak.Tenants.Application.Common.Builders;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Flows;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Services;
using Otg.Keycloak.Tenants.Application.Dto;
using Otg.Keycloak.Tenants.Domain.Aggregates;
using Otg.Keycloak.Tenants.Domain.Entities;

namespace Otg.Keycloak.Tenants.Application.UnitTests.Common.Builders;

public class OlpClientBuilderTests{

    private TokenDto mockTokenDto;
    private RealmRequest realmRequest;
    private IClientService _clientService;
    private IAuthFlowService _authFlowService;
    private IConfiguration _configuration;

    public OlpClientBuilderTests(){
        mockTokenDto = TestHelpers.mockTokenDto;

        realmRequest = TestHelpers.realmRequest;

        _clientService = Substitute.For<IClientService>();
        _authFlowService = Substitute.For<IAuthFlowService>();
        _configuration = Substitute.For<IConfiguration>();
    }

    public static List<AuthFlowDto> BuildAuthFlows(){

        var authFlow1 = new AuthFlowDto
        {
            Alias = "Legacy OLP Auth",
            ProviderId = "basic-flow",
            TopLevel = true,
            BuiltIn = false,
            Description = "A test authentication flow"
        };

        var authFlow2 = new AuthFlowDto{
            Alias = "OLP Browser",
            ProviderId = "basic-flow",
            TopLevel = true,
            BuiltIn = false,
            Description = "A test authentication flow"
        };

        var authFlow3 = new AuthFlowDto{
            Alias = "Some Other Flow",
            ProviderId = "basic-flow",
            TopLevel = true,
            BuiltIn = false,
            Description = "A test authentication flow"
        };
        return new List<AuthFlowDto>{authFlow1,authFlow2,authFlow3 };
    }

    [Fact]
    public async Task TestBuildClientScopeMappingsCreatesProtocolMappings(){
        var _clientId = Guid.NewGuid();

        var olpClientBuilder = new OlpClientBuilder(_clientService,
             _authFlowService,
            _configuration);
        
        var methodInfo = typeof(OlpClientBuilder).GetMethod("BuildClientScopeMappings", BindingFlags.NonPublic | BindingFlags.Instance);
        await (Task)methodInfo.Invoke(olpClientBuilder, new object[] { realmRequest, _clientId });

        await _clientService.Received(2).CreateProtocolMapper(Arg.Any<RealmRequest>(),
            Arg.Any<Guid>(),
            Arg.Any<ProtocolMapperDto?>());
    }

    [Fact]
    public async Task Given_ExpectedSuccessApiCalls_When_Build_Then_ExecuteBuildFUnctionWithExpectedCalls()
    {
        var flowAlias = "OLP Browser";
        var execution1 = new JObject { ["id"] = "exec1", ["requirement"] = "OPTIONAL" };
        var execution2 = new JObject { ["id"] = "exec2", ["requirement"] = "ALTERNATIVE" };
        var authFlowExecutions = new List<JObject> { execution1, execution2 };

        var olpClientBuilder = new OlpClientBuilder(_clientService,
            _authFlowService,
            _configuration);

        var subFlowAlias = "User Sign In";
        _authFlowService.GetFlowExecutions(realmRequest, subFlowAlias).Returns(Task.FromResult(authFlowExecutions));

        _authFlowService.GetFlowExecutions(realmRequest, flowAlias).Returns(Task.FromResult(authFlowExecutions));
        _authFlowService.GetFlowExecutions(realmRequest, "Legacy OLP Auth").Returns(Task.FromResult(authFlowExecutions));

        var authFlows = BuildAuthFlows();

        _authFlowService.GetAuthFlows(Arg.Any<RealmRequest>()).Returns(Task.FromResult(authFlows));

        _clientService.GetClient(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns(new ClientDto
        {
            Id = Guid.NewGuid(),
            ClientId = "clientid",
            Name = null,
            Protocol = null,
            Enabled = false,
            PublicClient = false,
            StandardFlowEnabled = false
        });

        // get the client id (keycloak guid) for this client.
        _clientService.GetClientAudienceKey(Arg.Any<RealmRequest>()).Returns("audiencekeyvalue");

        // get the tenant id (guid) for this client.
        _clientService.GetRealmAttribute(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns("tenantid");

        // Mock service account methods
        _clientService.GetServiceAccountUserId(Arg.Any<RealmRequest>(), Arg.Any<Guid>()).Returns("service-account-user-id");
        _clientService.GetClientRoleRepresentation(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>()).Returns(new JObject { ["id"] = "role-id", ["name"] = "service-role" });
        _clientService.AssignClientRoleToUser(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>(), Arg.Any<JObject>()).Returns(Task.CompletedTask);

        await olpClientBuilder.Build(realmRequest);

        await _clientService.Received(2).CreateProtocolMapper(Arg.Any<RealmRequest>(),
            Arg.Any<Guid>(),
            Arg.Any<ProtocolMapperDto?>());

        await _clientService.Received(4).CreateProtocolMapper(Arg.Any<RealmRequest>(),
            Arg.Any<Guid>(),
            Arg.Any<string>());

        await _clientService.Received(5).AddRole(Arg.Any<RealmRequest>(),
            Arg.Any<Guid>(),
            Arg.Any<AddRoleDto>());
    }

    [Fact]
    public async Task TestBuildBrowserFlowCallsAuthFlowServiceMethods(){
        
        var execution1 = new JObject { ["id"] = "exec1", ["requirement"] = "OPTIONAL" };
        var execution2 = new JObject { ["id"] = "exec2", ["requirement"] = "ALTERNATIVE" };
        var authFlowExecutions = new List<JObject> { execution1, execution2 };
        var flowAlias = "OLP Browser";
        var subFlowAlias = "User Sign In";
        _authFlowService.GetFlowExecutions(realmRequest, subFlowAlias).Returns(Task.FromResult(authFlowExecutions));

        _authFlowService.GetFlowExecutions(realmRequest, flowAlias).Returns(Task.FromResult(authFlowExecutions));
        _authFlowService.GetFlowExecutions(realmRequest, "Legacy OLP Auth").Returns(Task.FromResult(authFlowExecutions));

        var authFlows = BuildAuthFlows();
        
        _authFlowService.GetAuthFlows(Arg.Any<RealmRequest>()).Returns(Task.FromResult(authFlows));

        var olpClientBuilder = new OlpClientBuilder(_clientService,
             _authFlowService,
            _configuration);

        var clientMock = new ClientDto{
            ClientId = "abc123",
            Name ="my testClient",
            Protocol = "myProtocl",
            Enabled = true,
            PublicClient =false,
            StandardFlowEnabled = true,
            Id = Guid.NewGuid()
        };

        _clientService.GetClient(Arg.Any<RealmRequest>(),Arg.Any<string>()).Returns(Task.FromResult(clientMock));

        var methodInfo = typeof(OlpClientBuilder).GetMethod("BuildBrowserFlow", BindingFlags.NonPublic | BindingFlags.Instance);
        await (Task)methodInfo.Invoke(olpClientBuilder, new object[] { realmRequest});

        await _authFlowService.Received(1).CreateAuthFlow(realmRequest,Arg.Any<AuthFlowDto>());

        // cerates subflow
        await _authFlowService.Received(1)
            .CreateExecutionFlow(realmRequest, "OLP Browser",
                Arg.Is<ExecutionFlow>(exflow => exflow.Provider == "registration-page-form" &&
                                                exflow.Type == "basic-flow"));

        await _authFlowService.Received(1)
            .CreateFlowExecution(realmRequest, "User Sign In",
                Arg.Is<AuthFlowExecutionDto>(authflow =>authflow.Provider == "auth-username-password-form" &&
                authflow.Requirement == "REQUIRED"));

        await _authFlowService.Received(1)
            .CreateFlowExecution(realmRequest, "User Sign In",
                Arg.Is<AuthFlowExecutionDto>(authflow =>authflow.Provider == "script-olp-authenticator.js" &&
                authflow.Requirement == "REQUIRED"));
        
        await _authFlowService.Received(1)
            .CreateFlowExecution(realmRequest,"OLP Browser",
                Arg.Is<AuthFlowExecutionDto>(authflow =>authflow.Provider == "auth-cookie" &&
                authflow.Requirement == "ALTERNATIVE"));

        await _authFlowService.Received(4).UpdateFlowExecution(realmRequest,Arg.Any<string>(),Arg.Any<string>());
    }

    [Fact]
    public async Task BuildsCallsBrowserFlow(){

        var clientMock = new ClientDto{
            ClientId = "abc123",
            Name ="my testClient",
            Protocol = "myProtocl",
            Enabled = true,
            PublicClient =false,
            StandardFlowEnabled = true,
            Id = Guid.NewGuid()
        };

        _clientService.GetClient(Arg.Any<RealmRequest>(),Arg.Any<string>()).Returns(Task.FromResult<ClientDto?>(clientMock));

        var execution1 = new JObject { ["id"] = "exec1", ["requirement"] = "OPTIONAL" };
        var execution2 = new JObject { ["id"] = "exec2", ["requirement"] = "ALTERNATIVE" };
        var authFlowExecutions = new List<JObject> { execution1, execution2 };
        var flowAlias = "OLP Browser";
        var subFlowAlias = "User Sign In";

        _authFlowService.GetFlowExecutions(realmRequest, flowAlias).Returns(Task.FromResult(authFlowExecutions));
        _authFlowService.GetFlowExecutions(realmRequest, subFlowAlias).Returns(Task.FromResult(authFlowExecutions));
        _authFlowService.GetFlowExecutions(realmRequest, "Legacy OLP Auth").Returns(Task.FromResult(authFlowExecutions));


        var authFlows = BuildAuthFlows();
        
        _authFlowService.GetAuthFlows(Arg.Any<RealmRequest>()).Returns(Task.FromResult(authFlows));

        var olpClientBuilder = new OlpClientBuilder(_clientService,
             _authFlowService,
            _configuration);

        // Mock service account methods
        _clientService.GetServiceAccountUserId(Arg.Any<RealmRequest>(), Arg.Any<Guid>()).Returns("service-account-user-id");
        _clientService.GetClientRoleRepresentation(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>()).Returns(new JObject { ["id"] = "role-id", ["name"] = "service-role" });
        _clientService.AssignClientRoleToUser(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>(), Arg.Any<JObject>()).Returns(Task.CompletedTask);
        _clientService.GetClientAudienceKey(Arg.Any<RealmRequest>()).Returns("audiencekeyvalue");
        _clientService.GetRealmAttribute(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns("tenantid");

        await olpClientBuilder.Build(realmRequest);
        await _clientService.OverrideAuthenticationFlow(realmRequest,Arg.Any<Guid>()
            ,Arg.Any<OverrideAuthenticationFlowDto>());

    }

    [Fact]
    public async Task Given_BuildOlpClient_Then_CorrectFunctionsCalled(){

        var clientMock = new ClientDto{
            ClientId = "abc123",
            Name ="my testClient",
            Protocol = "myProtocl",
            Enabled = true,
            PublicClient =false,
            StandardFlowEnabled = true,
            Id = Guid.NewGuid()
        };

        _clientService.GetClient(Arg.Any<RealmRequest>(),Arg.Any<string>()).Returns(Task.FromResult<ClientDto?>(clientMock));

        var execution1 = new JObject { ["id"] = "exec1", ["requirement"] = "OPTIONAL" };
        var execution2 = new JObject { ["id"] = "exec2", ["requirement"] = "ALTERNATIVE" };
        var authFlowExecutions = new List<JObject> { execution1, execution2 };
        var flowAlias = "OLP Browser";
        var subFlowAlias = "User Sign In";

        _authFlowService.GetFlowExecutions(realmRequest, flowAlias).Returns(Task.FromResult(authFlowExecutions));
        _authFlowService.GetFlowExecutions(realmRequest, subFlowAlias).Returns(Task.FromResult(authFlowExecutions));
        _authFlowService.GetFlowExecutions(realmRequest, "Legacy OLP Auth").Returns(Task.FromResult(authFlowExecutions));


        var authFlows = BuildAuthFlows();
        
        _authFlowService.GetAuthFlows(Arg.Any<RealmRequest>()).Returns(Task.FromResult(authFlows));

        var olpClientBuilder = new OlpClientBuilder(_clientService,
             _authFlowService,
            _configuration);

        // Mock service account methods
        _clientService.GetServiceAccountUserId(Arg.Any<RealmRequest>(), Arg.Any<Guid>()).Returns("service-account-user-id");
        _clientService.GetClientRoleRepresentation(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>()).Returns(new JObject { ["id"] = "role-id", ["name"] = "service-role" });
        _clientService.AssignClientRoleToUser(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>(), Arg.Any<JObject>()).Returns(Task.CompletedTask);
        _clientService.GetClientAudienceKey(Arg.Any<RealmRequest>()).Returns("audiencekeyvalue");
        _clientService.GetRealmAttribute(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns("tenantid");

        await olpClientBuilder.Build(realmRequest);
        await _clientService.Received(2).CreateClient(realmRequest,Arg.Any<JObject>());
        await _clientService.Received(5).AddRole(realmRequest,Arg.Any<Guid>(),Arg.Any<AddRoleDto>());
        await _clientService.Received(2).CreateProtocolMapper(realmRequest,Arg.Any<Guid>(),Arg.Any<ProtocolMapperDto?>());
        await _clientService.Received(2).GetClientAudienceKey(realmRequest);
        await _clientService.Received(2).GetRealmAttribute(realmRequest, "otgTenantId");
        await _clientService.OverrideAuthenticationFlow(realmRequest,Arg.Any<Guid>()
            ,Arg.Any<OverrideAuthenticationFlowDto>());

        await _authFlowService.Received(1).CreateAuthFlow(realmRequest,Arg.Any<AuthFlowDto>());
    }

}