using Newtonsoft.Json.Linq;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Services;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Builders;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Flows;
using Otg.Keycloak.Tenants.Application.Common.Builders;
using Otg.Keycloak.Tenants.Application.Dto;
using Otg.Keycloak.Tenants.Domain.Aggregates;
using Otg.Keycloak.Tenants.Domain.Constants;
using Otg.Keycloak.Tenants.Application.UnitTests;

namespace Otg.Keycloak.Tenants.Application.UnitTests.Common.Builders;

public class ClientBuilderTests
{
    private readonly IClientService _clientService;
    private readonly IAuthFlowService _authFlowService;
    private readonly RealmRequest _realmRequest;
    private readonly TestClientBuilder _clientBuilder;

    public ClientBuilderTests()
    {
        _clientService = Substitute.For<IClientService>();
        _authFlowService = Substitute.For<IAuthFlowService>();
        _realmRequest = TestHelpers.realmRequest;
        _clientBuilder = new TestClientBuilder(_clientService, _authFlowService);
    }

    [Fact]
    public async Task Build_CallsAddUniversalServiceRole_WhenAddingServiceAccount()
    {
        // Arrange
        var mainClientId = Guid.NewGuid();
        var serviceClientId = Guid.NewGuid();
        var serviceAccountUserId = "service-account-user-id";
        var serviceRole = new JObject 
        { 
            ["id"] = "service-role-id",
            ["name"] = "service-role"
        };

        // Mock the main client
        _clientService.GetClient(_realmRequest, "TestClient")
            .Returns(new ClientDto {
                Id = mainClientId,
                ClientId = "TestClient",
                Name = "TestClient",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = true
            });

        // Mock the service client
        _clientService.GetClient(_realmRequest, "TestClient-service")
            .Returns(new ClientDto {
                Id = serviceClientId,
                ClientId = "TestClient-service",
                Name = "TestClient-service",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = false
            });

        // Mock service account user ID retrieval
        _clientService.GetServiceAccountUserId(_realmRequest, serviceClientId)
            .Returns(serviceAccountUserId);

        // Mock role retrieval
        _clientService.GetClientRoleRepresentation(_realmRequest, serviceClientId, "service-role")
            .Returns(serviceRole);

        // Act
        await _clientBuilder.Build(_realmRequest);

        // Assert - Verify the universal service role workflow was called
        await _clientService.Received(1).AddRole(_realmRequest, serviceClientId, 
            Arg.Is<AddRoleDto>(role => role.Name == "service-role"));
        
        await _clientService.Received(1).GetServiceAccountUserId(_realmRequest, serviceClientId);
        
        await _clientService.Received(1).GetClientRoleRepresentation(_realmRequest, serviceClientId, "service-role");
        
        await _clientService.Received(1).AssignClientRoleToUser(_realmRequest, serviceClientId, serviceAccountUserId, serviceRole);
    }

    [Fact]
    public async Task Build_ThrowsMissingFieldException_WhenServiceAccountUserIdIsNull()
    {
        // Arrange
        var mainClientId = Guid.NewGuid();
        var serviceClientId = Guid.NewGuid();

        _clientService.GetClient(_realmRequest, "TestClient")
            .Returns(new ClientDto {
                Id = mainClientId,
                ClientId = "TestClient",
                Name = "TestClient",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = true
            });

        _clientService.GetClient(_realmRequest, "TestClient-service")
            .Returns(new ClientDto {
                Id = serviceClientId,
                ClientId = "TestClient-service",
                Name = "TestClient-service",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = false
            });

        // Return null for service account user ID
        _clientService.GetServiceAccountUserId(_realmRequest, serviceClientId)
            .Returns((string?)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<MissingFieldException>(
            () => _clientBuilder.Build(_realmRequest));
        
        exception.Message.Should().Contain("missing service account user");
    }

    [Fact]
    public async Task Build_ThrowsMissingFieldException_WhenServiceAccountUserIdIsEmpty()
    {
        // Arrange
        var mainClientId = Guid.NewGuid();
        var serviceClientId = Guid.NewGuid();

        _clientService.GetClient(_realmRequest, "TestClient")
            .Returns(new ClientDto {
                Id = mainClientId,
                ClientId = "TestClient",
                Name = "TestClient",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = true
            });

        _clientService.GetClient(_realmRequest, "TestClient-service")
            .Returns(new ClientDto {
                Id = serviceClientId,
                ClientId = "TestClient-service",
                Name = "TestClient-service",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = false
            });

        // Return empty string for service account user ID
        _clientService.GetServiceAccountUserId(_realmRequest, serviceClientId)
            .Returns(string.Empty);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<MissingFieldException>(
            () => _clientBuilder.Build(_realmRequest));
        
        exception.Message.Should().Contain("missing service account user");
    }

    [Fact]
    public async Task Build_ThrowsMissingFieldException_WhenServiceRoleIsNull()
    {
        // Arrange
        var mainClientId = Guid.NewGuid();
        var serviceClientId = Guid.NewGuid();
        var serviceAccountUserId = "service-account-user-id";

        _clientService.GetClient(_realmRequest, "TestClient")
            .Returns(new ClientDto {
                Id = mainClientId,
                ClientId = "TestClient",
                Name = "TestClient",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = true
            });

        _clientService.GetClient(_realmRequest, "TestClient-service")
            .Returns(new ClientDto {
                Id = serviceClientId,
                ClientId = "TestClient-service",
                Name = "TestClient-service",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = false
            });

        _clientService.GetServiceAccountUserId(_realmRequest, serviceClientId)
            .Returns(serviceAccountUserId);

        // Return null for service role
        _clientService.GetClientRoleRepresentation(_realmRequest, serviceClientId, "service-role")
            .Returns((JObject?)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<MissingFieldException>(
            () => _clientBuilder.Build(_realmRequest));
        
        exception.Message.Should().Contain("missing service-role role");
    }

    [Fact]
    public async Task Build_CreatesServiceClientWithCorrectTemplate()
    {
        // Arrange
        var mainClientId = Guid.NewGuid();
        var serviceClientId = Guid.NewGuid();
        var serviceAccountUserId = "service-account-user-id";
        var serviceRole = new JObject { ["id"] = "service-role-id", ["name"] = "service-role" };

        _clientService.GetClient(_realmRequest, "TestClient")
            .Returns(new ClientDto {
                Id = mainClientId,
                ClientId = "TestClient",
                Name = "TestClient",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = true
            });

        _clientService.GetClient(_realmRequest, "TestClient-service")
            .Returns(new ClientDto {
                Id = serviceClientId,
                ClientId = "TestClient-service",
                Name = "TestClient-service",
                Protocol = "openid-connect",
                Enabled = true,
                PublicClient = false,
                StandardFlowEnabled = false
            });

        _clientService.GetServiceAccountUserId(_realmRequest, serviceClientId)
            .Returns(serviceAccountUserId);

        _clientService.GetClientRoleRepresentation(_realmRequest, serviceClientId, "service-role")
            .Returns(serviceRole);

        // Act
        await _clientBuilder.Build(_realmRequest);

        // Assert - Verify service client creation was called
        await _clientService.Received(1).CreateClient(_realmRequest, Arg.Is<JObject>(client => 
            client.ToString().Contains("TestClient-service")));
    }
}

// Test implementation of abstract ClientBuilder for testing purposes
public class TestClientBuilder : ClientBuilder
{
    public TestClientBuilder(IClientService clientService, IAuthFlowService authFlowService) 
        : base(clientService, authFlowService)
    {
    }

    public override string ClientId => "TestClient";
    public override bool ClientEnabled => true;
    public override ClientType ClientType => ClientType.OceanLaunch;
    public override List<string> ClientRoles => new() { "test-role-1", "test-role-2" };
}
