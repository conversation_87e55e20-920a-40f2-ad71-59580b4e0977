using Newtonsoft.Json.Linq;
using Otg.Keycloak.Tenants.Application.Common.Builders;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Flows;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Services;
using Otg.Keycloak.Tenants.Application.Dto;
using Otg.Keycloak.Tenants.Domain.Aggregates;

namespace Otg.Keycloak.Tenants.Application.UnitTests.Common.Builders;

public class OceanLaunchClientBuilderTests
{

    private readonly RealmRequest _realmRequest = TestHelpers.realmRequest;
    private readonly IClientService _clientService = Substitute.For<IClientService>();
    private readonly IAuthFlowService _authFlowService = Substitute.For<IAuthFlowService>();

    public static List<AuthFlowDto> BuildAuthFlows()
    {

        var authFlow1 = new AuthFlowDto
        {
            Alias = "OceanAlias",
            ProviderId = "basic-flow",
            TopLevel = true,
            BuiltIn = false,
            Description = "A test authentication flow"
        };

        var authFlow2 = new AuthFlowDto
        {
            Alias = "OceanAlias",
            ProviderId = "basic-flow",
            TopLevel = true,
            BuiltIn = false,
            Description = "A test authentication flow"
        };

        var authFlow3 = new AuthFlowDto
        {
            Alias = "OceanAlias",
            ProviderId = "basic-flow",
            TopLevel = true,
            BuiltIn = false,
            Description = "A test authentication flow"
        };
        return new List<AuthFlowDto> { authFlow1, authFlow2, authFlow3 };
    }

    [Fact]
    public async Task TestSetFlowOverrideCallsOverrideAuthenticationFlow()
    {
        var clientMock = new ClientDto
        {
            ClientId = "abc123",
            Name = "my testClient",
            Protocol = "myProtocl",
            Enabled = true,
            PublicClient = false,
            StandardFlowEnabled = true,
            Id = Guid.NewGuid()
        };

        var authFlows = BuildAuthFlows();
        _clientService.GetClient(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns(Task.FromResult(clientMock));
        _authFlowService.GetAuthFlows(Arg.Any<RealmRequest>()).Returns(Task.FromResult(authFlows));
        var oceanLaunchClientBuilder = new OceanLaunchClientBuilder(_clientService, _authFlowService);

        await oceanLaunchClientBuilder.SetAuthFlowOverride(_realmRequest, "OceanAlias");
        await _clientService.Received(1).OverrideAuthenticationFlow(_realmRequest,
            (Guid)clientMock.Id, Arg.Any<OverrideAuthenticationFlowDto>());
    }

    [Fact]
    public async Task Given_ExpectedApiSuccessCalls_When_Build_Then_ExecuteBuildFunctionWithExpectedCalls()
    {
        var clientService = Substitute.For<IClientService>();
        var authFlowService = Substitute.For<IAuthFlowService>();

        clientService.GetClient(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns(new ClientDto
        {
            Id = Guid.NewGuid(),
            ClientId = "clientid",
            Name = null,
            Protocol = null,
            Enabled = false,
            PublicClient = false,
            StandardFlowEnabled = false
        });

        // get the client id (keycloak guid) for this client.
        clientService.GetClientAudienceKey(Arg.Any<RealmRequest>()).Returns("audiencekeyvalue");

        // Mock service account methods
        clientService.GetServiceAccountUserId(Arg.Any<RealmRequest>(), Arg.Any<Guid>()).Returns("service-account-user-id");
        clientService.GetClientRoleRepresentation(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>()).Returns(new JObject { ["id"] = "role-id", ["name"] = "service-role" });
        clientService.AssignClientRoleToUser(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>(), Arg.Any<JObject>()).Returns(Task.CompletedTask);
        clientService.GetRealmAttribute(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns("tenantid");

        var oceanLaunchClientBuilder = new OceanLaunchClientBuilder(clientService, authFlowService);
        await oceanLaunchClientBuilder.Build(_realmRequest);

        await clientService.Received(2).CreateClient(Arg.Any<RealmRequest>(), Arg.Any<JObject>());

        await clientService.Received(17).AddRole(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<AddRoleDto>());
    }
}