using Newtonsoft.Json.Linq;
using Otg.Keycloak.Tenants.Application.Common.Builders;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Flows;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Services;
using Otg.Keycloak.Tenants.Application.Dto;
using Otg.Keycloak.Tenants.Domain.Aggregates;

namespace Otg.Keycloak.Tenants.Application.UnitTests.Common.Builders;

public class GeneralClientBuilderTests
{
    private readonly RealmRequest _realmRequest = TestHelpers.realmRequest;
    private readonly IClientService _clientService = Substitute.For<IClientService>();
    private readonly IAuthFlowService _authFlowService = Substitute.For<IAuthFlowService>();

    [Fact]
    public async Task Given_InstantiateGeneralClientBuilder_Then_PropertiesAreCorrect()
    {
        // Mock service account methods
        _clientService.GetServiceAccountUserId(Arg.Any<RealmRequest>(), Arg.Any<Guid>()).Returns("service-account-user-id");
        _clientService.GetClientRoleRepresentation(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>()).Returns(new JObject { ["id"] = "role-id", ["name"] = "service-role" });
        _clientService.AssignClientRoleToUser(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<string>(), Arg.Any<JObject>()).Returns(Task.CompletedTask);
        _clientService.GetClientAudienceKey(Arg.Any<RealmRequest>()).Returns("audiencekeyvalue");
        _clientService.GetRealmAttribute(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns("tenantid");
        _clientService.GetClient(Arg.Any<RealmRequest>(), Arg.Any<string>()).Returns(new ClientDto
        {
            Id = Guid.NewGuid(),
            ClientId = "testClientId-service",
            Name = "testClientId-service",
            Protocol = "openid-connect",
            Enabled = true,
            PublicClient = false,
            StandardFlowEnabled = false
        });

        var generalClientBuilder = new GeneralClientBuilder(_clientService, _authFlowService);
        await generalClientBuilder.BuildGeneralClient("testClientId", false, _realmRequest);
        var client = generalClientBuilder.Client;
        var roles = generalClientBuilder.ClientRoles;

        client["enabled"].Should().NotBeNull();
        client["enabled"]!.Value<string>().Should().Be("False");
        roles.Count.Should().Be(4, "the standard number of roles for a client is 4");
        roles.Should().Contain("app-testclientid-user");
        roles.Should().Contain("app-testclientid-admin");
        roles.Should().Contain("app-testclientid-supervisor");
        roles.Should().Contain("app-testclientid-observer");

        await _clientService.Received(5).AddRole(Arg.Any<RealmRequest>(), Arg.Any<Guid>(), Arg.Any<AddRoleDto>());
    }
}