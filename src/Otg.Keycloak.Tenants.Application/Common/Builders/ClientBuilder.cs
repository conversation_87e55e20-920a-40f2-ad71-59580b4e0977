using Newtonsoft.Json.Linq;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Builders;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Flows;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Services;
using Otg.Keycloak.Tenants.Application.Dto;
using Otg.Keycloak.Tenants.Domain.Aggregates;
using Otg.Keycloak.Tenants.Domain.Constants;

namespace Otg.Keycloak.Tenants.Application.Common.Builders;

public abstract class ClientBuilder(IClientService clientService, IAuthFlowService authFlowService) : IClientBuilder
{
    protected IClientService ClientService => clientService;

    public abstract string ClientId { get;}
    public abstract bool ClientEnabled {get;}
    public abstract ClientType ClientType { get; }
    public string BackChannelLogoutUrl => "";
    public List<AuthFlowExecutionDto> AuthFlowExecutions => new();

    public abstract List<string> ClientRoles { get; }

    public JObject Client {
        get
        {
            var clientTemplate = Properties.Resources.realm_template_client!;
            clientTemplate = clientTemplate.Replace("<<CLIENTID>>", ClientId);
            clientTemplate = clientTemplate.Replace("<<BACKCHANNEL-LOGOUT-URL>>", BackChannelLogoutUrl);
            clientTemplate = clientTemplate.Replace("<<CLIENT-ENABLED>>", ClientEnabled.ToString());
            return JObject.Parse(clientTemplate);
        }
    }

    public AuthFlowDto AuthFlow => new()
    {
        Alias = $"{ClientId} Browser", ProviderId = "basic-flow", BuiltIn = false, TopLevel = true,
    };

    public async Task AddAudienceMappers(RealmRequest request)
    {
        // get the client id (keycloak guid) for this client.
        var clientId = await GetClientId(request);

        await AddAudienceMappers(request, clientId);
    }

    public async Task AddAudienceMappers(RealmRequest request, Guid clientId)
    {
        // get the client id (keycloak guid) for this client.
        var audienceKey = await ClientService.GetClientAudienceKey(request);

        if (string.IsNullOrWhiteSpace(audienceKey)) return;

        var audienceMapperPayload = Properties.Resources.realm_template_audiencemapper;
        audienceMapperPayload = audienceMapperPayload.Replace("<<MAPPER-NAME>>", $"{ClientId} Audience Key");
        audienceMapperPayload = audienceMapperPayload.Replace("<<MAPPER-VALUE>>", audienceKey);

        await ClientService.CreateProtocolMapper(
            request,
            clientId,
            audienceMapperPayload
        );
    }

    private async Task<Guid> GetClientId(RealmRequest request)
    {
        var client = await ClientService.GetClient(request, ClientId);
        return client?.Id ?? Guid.Empty;
    }

    private async Task<Guid> GetClientId(RealmRequest request, string clientIdString)
    {
        var client = await ClientService.GetClient(request, clientIdString);
        return client?.Id ?? Guid.Empty;
    }

    public async Task AddClientRoles(RealmRequest request)
    {
        var clientId = await GetClientId(request);

        foreach (var role in ClientRoles)
        {
            await ClientService.AddRole(request, clientId, new AddRoleDto
            {
                Name = role
            });
        }
    }

    public async Task TenantIdTokenClaim(RealmRequest request)
    {
        // get the client id (keycloak guid) for this client.
        var clientId = await GetClientId(request);
        await TenantIdTokenClaim(request, clientId);
    }

    /// <summary>
    /// Adds the tenant id stored against a realm to the token claim for this client.
    /// </summary>
    /// <param name="request">Core request data</param>
    /// <param name="clientId">Guid id for this Keycloak client</param>
    /// <returns></returns>
    public async Task TenantIdTokenClaim(RealmRequest request, Guid clientId)
    {
        // get the client id (keycloak guid) for this client.
        var tenantId = await ClientService.GetRealmAttribute(request, "otgTenantId");

        if (string.IsNullOrWhiteSpace(tenantId)) return;

        var audienceMapperPayload = Properties.Resources.realm_template_hardcodedmapper;
        audienceMapperPayload = audienceMapperPayload.Replace("<<MAPPER-NAME>>", $"{ClientId} Tenant Id");
        audienceMapperPayload = audienceMapperPayload.Replace("<<MAPPER-VALUE>>", tenantId);
        audienceMapperPayload = audienceMapperPayload.Replace("<<CLAIM-NAME>>", "otg_tenant_id");

        await ClientService.CreateProtocolMapper(
            request,
            clientId,
            audienceMapperPayload
        );
    }

    public async Task SetAuthFlowOverride(RealmRequest request, string authFlowAlias)
    {
        // find the auth flow matching the requested alias to get the id.
        var authFlow = await authFlowService.GetAuthFlows(request);
        var authFlowFound = authFlow.FirstOrDefault(flow =>
            string.Equals(flow.Alias, authFlowAlias, StringComparison.OrdinalIgnoreCase));
        if (authFlowFound == null) throw new MissingFieldException($"missing {authFlowAlias} auth flow");

        // get the client id.
        var clientId = await GetClientId(request);

        // update the browser flow override.
        await ClientService.OverrideAuthenticationFlow(
            request,
            clientId,
            new OverrideAuthenticationFlowDto
            {
                AuthenticationFlowBindingOverrides = new AuthenticationFlowBindingOverridesDto
                {
                    Browser = authFlowFound.Id
                },
            }
        );
    }

    public async Task Build(RealmRequest request, Dictionary<string, string>? claims = null)
    {
        // build the client.
        await ClientService.CreateClient(request, Client);

        // add client roles.
        await AddClientRoles(request);

        // add the realm audience mapper to this client.
        await AddAudienceMappers(request);

        // adds the tenantid claim against the client.
        await TenantIdTokenClaim(request);

        // add additional claims provided.
        if (claims is { Count: > 0 })
        {
            foreach (var claim in claims)
            {
                await AddTokenClaim(request, claim.Key, claim.Value);
            }
        }

        // add service account for this client.
        await AddServiceAccount(request);
    }

    private async Task AddServiceAccount(RealmRequest request)
    {
        var serviceClientId = $"{ClientId}-service";

        var clientTemplate = Properties.Resources.realm_template_service_client!;
        clientTemplate = clientTemplate.Replace("<<CLIENTID>>", serviceClientId);

        // build the client.
        await ClientService.CreateClient(request, JObject.Parse(clientTemplate));

        var clientId = await GetClientId(request, serviceClientId);

        // add the realm audience mapper to this client.
        await AddAudienceMappers(request, clientId);

        // adds the tenantid claim against the client.
        await TenantIdTokenClaim(request, clientId);
        
        // Add the universal service role.
        await AddUniversalServiceRole(request, clientId);
    }

    /// <summary>
    /// Adds the universal service role to the service account user for this client.
    /// </summary>
    /// <param name="request"></param>
    /// <param name="clientId"></param>
    /// <exception cref="MissingFieldException"></exception>
    private async Task AddUniversalServiceRole(RealmRequest request, Guid clientId)
    {
        var serviceRoleName = "service-role";
        await clientService.AddRole(request, clientId, new AddRoleDto
        {
            Name = serviceRoleName
        });
        
        var serviceAccountUserId = await clientService.GetServiceAccountUserId(request, clientId);
        
        if (string.IsNullOrWhiteSpace(serviceAccountUserId)) throw new MissingFieldException($"missing service account user");
        
        var serviceRole = await clientService.GetClientRoleRepresentation(request, clientId, serviceRoleName);
        
        if (serviceRole == null) throw new MissingFieldException($"missing {serviceRoleName} role");
        
        await clientService.AssignClientRoleToUser(request, clientId, serviceAccountUserId, serviceRole);
    }

    /// <summary>
    /// Adds a token claim to the client.
    /// </summary>
    /// <param name="request"></param>
    /// <param name="claimKey"></param>
    /// <param name="claimValue"></param>
    private async Task AddTokenClaim(RealmRequest request, string claimKey, string claimValue)
    {
        // get the client id (keycloak guid) for this client.
        var clientId = await GetClientId(request);

        var audienceMapperPayload = Properties.Resources.realm_template_hardcodedmapper;
        audienceMapperPayload = audienceMapperPayload.Replace("<<MAPPER-NAME>>", $"{ClientId} {claimKey}");
        audienceMapperPayload = audienceMapperPayload.Replace("<<MAPPER-VALUE>>", claimValue);
        audienceMapperPayload = audienceMapperPayload.Replace("<<CLAIM-NAME>>", claimKey);

        await ClientService.CreateProtocolMapper(
            request,
            clientId,
            audienceMapperPayload
        );
    }
}